<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 开发者工具检测状态显示 -->
      <div class="devtools-status-card" style="margin-bottom: 20px;">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>开发者工具检测状态</span>
              <el-tag :type="isDevToolsOpen ? 'danger' : 'success'">
                {{ isDevToolsOpen ? '检测到开发者工具' : '正常状态' }}
              </el-tag>
            </div>
          </template>
          <div>
            <p><strong>检测功能:</strong> {{ detectionEnabled ? '已启用' : '已禁用' }}</p>
            <p><strong>当前状态:</strong> {{ isDevToolsOpen ? '开发者工具已打开' : '开发者工具未打开' }}</p>
            <p><strong>API请求:</strong> {{ shouldBlockRequests ? '已阻止' : '正常' }}</p>
            <p style="margin-top: 15px; color: #666; font-size: 14px;">
              提示：打开浏览器开发者工具（F12）来测试检测功能
            </p>
          </div>
        </el-card>
      </div>

      <splitpanes>
        <pane size="70">
          <splitpanes horizontal>
            <pane size="25">
              <current-user/>
            </pane>
            <pane size="75">
              <favorite/>
            </pane>
          </splitpanes>
        </pane>
        <pane size="30">
          <splitpanes horizontal>
            <pane size="58">
              <schedule/>
            </pane>
            <pane size="42">
              <sys-log/>
            </pane>
          </splitpanes>
        </pane>
      </splitpanes>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { defineAsyncComponent } from 'vue';
import { useDevToolsDetection } from '/@/hooks/useDevToolsDetection';

const CurrentUser = defineAsyncComponent(() => import('./current-user.vue'));
const Favorite = defineAsyncComponent(() => import('./favorite.vue'));
const Schedule = defineAsyncComponent(() => import('./schedule.vue'));
const SysLog = defineAsyncComponent(() => import('./sys-log.vue'));

// 获取开发者工具检测状态
const {
  isDevToolsOpen,
  detectionEnabled,
  shouldBlockRequests
} = useDevToolsDetection();
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
