<template>
  <div class="layout-padding">
    <div class="layout-padding-auto layout-padding-view">
      <!-- 开发者工具检测状态显示 -->
      <div class="devtools-status-card" style="margin-bottom: 20px;">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>开发者工具检测状态</span>
              <el-tag :type="isDevToolsOpen ? 'danger' : 'success'">
                {{ isDevToolsOpen ? '检测到开发者工具' : '正常状态' }}
              </el-tag>
            </div>
          </template>
          <div>
            <p><strong>检测功能:</strong> {{ detectionEnabled ? '已启用' : '已禁用' }}</p>
            <p><strong>当前状态:</strong> {{ isDevToolsOpen ? '开发者工具已打开' : '开发者工具未打开' }}</p>
            <p><strong>API请求:</strong> {{ shouldBlockRequests ? '已阻止' : '正常' }}</p>
            <div style="margin-top: 15px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
              <p style="margin: 0 0 8px 0; font-weight: 600; color: #333;">检测说明：</p>
              <p style="margin: 0 0 5px 0; font-size: 13px; color: #666;">
                • 系统使用8种方法检测开发者工具，包括窗口大小、控制台监听、调试器检测等
              </p>
              <p style="margin: 0 0 5px 0; font-size: 13px; color: #666;">
                • 即使将开发者工具最小化，系统仍能通过多种方法检测到其存在
              </p>
              <p style="margin: 0 0 5px 0; font-size: 13px; color: #666;">
                • 检测功能通过 themeConfig.ts 中的 isDevToolsDetectionEnabled 配置控制
              </p>
              <p style="margin: 0; font-size: 13px; color: #666;">
                • 系统会在检测到开发者工具后阻止所有API请求并显示警告页面
              </p>
            </div>
            <p style="margin-top: 15px; color: #666; font-size: 14px;">
              <strong>测试方法：</strong>按 F12 打开开发者工具，然后尝试调整其大小或最小化
            </p>
          </div>
        </el-card>
      </div>

      <splitpanes>
        <pane size="70">
          <splitpanes horizontal>
            <pane size="25">
              <current-user/>
            </pane>
            <pane size="75">
              <favorite/>
            </pane>
          </splitpanes>
        </pane>
        <pane size="30">
          <splitpanes horizontal>
            <pane size="58">
              <schedule/>
            </pane>
            <pane size="42">
              <sys-log/>
            </pane>
          </splitpanes>
        </pane>
      </splitpanes>
    </div>
  </div>
</template>

<script setup lang="ts" name="home">
import { defineAsyncComponent, computed } from 'vue';
import { useDevToolsDetection } from '/@/hooks/useDevToolsDetection';

const CurrentUser = defineAsyncComponent(() => import('./current-user.vue'));
const Favorite = defineAsyncComponent(() => import('./favorite.vue'));
const Schedule = defineAsyncComponent(() => import('./schedule.vue'));
const SysLog = defineAsyncComponent(() => import('./sys-log.vue'));

// 获取开发者工具检测状态
const {
  isDevToolsOpen,
  detectionEnabled,
  shouldBlockRequests
} = useDevToolsDetection();

// 检测当前环境
const isDev = computed(() => import.meta.env.DEV);
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
