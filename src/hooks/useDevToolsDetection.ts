import { ref, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useDevToolsDetectionStore } from '/@/stores/devToolsDetection';
import { initDevToolsDetection, stopDevToolsDetection, DevToolsDetection } from '/@/utils/devToolsDetection';
import mittBus from '/@/utils/mitt';

/**
 * 开发者工具检测组合式函数
 */
export function useDevToolsDetection() {
	const themeConfigStore = useThemeConfig();
	const devToolsStore = useDevToolsDetectionStore();
	const { themeConfig } = storeToRefs(themeConfigStore);
	
	const detection = ref<DevToolsDetection | null>(null);

	/**
	 * 初始化检测
	 */
	const initDetection = () => {
		// 检查是否启用检测
		if (!themeConfig.value.isDevToolsDetectionEnabled) {
			devToolsStore.setDetectionEnabled(false);
			return;
		}

		devToolsStore.setDetectionEnabled(true);

		// 延迟启动检测，避免影响应用初始化
		const startDelay = 3000;

		setTimeout(() => {
			// 初始化检测器
			detection.value = initDevToolsDetection({
				interval: 1000, // 每秒检测一次
				enabled: true,
				onDetected: () => {
					console.warn('开发者工具已被检测到，系统将进入安全模式');
					devToolsStore.setDevToolsOpen(true);
				},
				onClosed: () => {
					console.info('开发者工具已关闭，系统恢复正常');
					devToolsStore.setDevToolsOpen(false);
				},
			});

			// 开始检测
			detection.value.start();
			devToolsStore.setInitialized();
		}, startDelay);
	};

	/**
	 * 停止检测
	 */
	const stopDetection = () => {
		if (detection.value) {
			detection.value.stop();
			detection.value = null;
		}
		stopDevToolsDetection();
		devToolsStore.reset();
	};

	/**
	 * 重新启动检测
	 */
	const restartDetection = () => {
		stopDetection();
		initDetection();
	};

	/**
	 * 更新检测配置
	 */
	const updateDetectionConfig = (enabled: boolean) => {
		if (enabled) {
			if (!detection.value) {
				initDetection();
			}
		} else {
			stopDetection();
		}
	};

	// 检查是否在组件上下文中
	const instance = getCurrentInstance();

	if (instance) {
		// 在组件上下文中，注册生命周期钩子
		onMounted(() => {
			// 延迟初始化，确保组件完全挂载
			nextTick(() => {
				initDetection();
			});

			// 监听配置变化
			mittBus.on('devToolsDetectionConfigChanged', (enabled: boolean) => {
				updateDetectionConfig(enabled);
			});
		});

		// 组件卸载时清理
		onUnmounted(() => {
			stopDetection();
			mittBus.off('devToolsDetectionConfigChanged', () => {});
		});
	} else {
		// 不在组件上下文中，立即初始化
		console.warn('useDevToolsDetection: Not in component context, initializing immediately');
		initDetection();

		// 监听配置变化
		mittBus.on('devToolsDetectionConfigChanged', (enabled: boolean) => {
			updateDetectionConfig(enabled);
		});
	}

	return {
		detection,
		initDetection,
		stopDetection,
		restartDetection,
		updateDetectionConfig,
		// 从 store 中获取状态
		isDevToolsOpen: storeToRefs(devToolsStore).isDevToolsOpen,
		showWarning: storeToRefs(devToolsStore).showWarning,
		detectionEnabled: storeToRefs(devToolsStore).detectionEnabled,
		shouldBlockRequests: storeToRefs(devToolsStore).shouldBlockRequests,
		shouldShowWarning: storeToRefs(devToolsStore).shouldShowWarning,
	};
}
