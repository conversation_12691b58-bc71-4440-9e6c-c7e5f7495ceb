<template>
	<div class="devtools-warning" v-show="visible">
		<div class="devtools-warning-overlay">
			<div class="devtools-warning-content">
				<div class="warning-icon">
					<SvgIcon name="ele-WarningFilled" class="warning-icon-svg" />
				</div>
				<div class="warning-title">{{ $t('devToolsWarning.title') }}</div>
				<div class="warning-message">{{ $t('devToolsWarning.message') }}</div>
				<div class="warning-details">
					<p>{{ $t('devToolsWarning.details1') }}</p>
					<p>{{ $t('devToolsWarning.details2') }}</p>
					<p>{{ $t('devToolsWarning.details3') }}</p>
				</div>
				<div class="warning-actions">
					<el-button type="primary" size="large" @click="handleRefresh">
						{{ $t('devToolsWarning.refreshBtn') }}
					</el-button>
					<el-button size="large" @click="handleContact">
						{{ $t('devToolsWarning.contactBtn') }}
					</el-button>
				</div>
				<div class="warning-footer">
					<p>{{ $t('devToolsWarning.footer') }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="DevToolsWarning">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';

// 定义组件属性
interface Props {
	visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
});

// 定义事件
const emit = defineEmits<{
	refresh: [];
	contact: [];
}>();

const { t } = useI18n();

// 刷新页面
const handleRefresh = () => {
	window.location.reload();
};

// 联系管理员
const handleContact = () => {
	ElMessage.info(t('devToolsWarning.contactMessage'));
	emit('contact');
};
</script>

<style scoped lang="scss">
.devtools-warning {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10px);
	display: flex;
	align-items: center;
	justify-content: center;

	.devtools-warning-overlay {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.devtools-warning-content {
		background: var(--el-bg-color);
		border-radius: 12px;
		padding: 60px 40px;
		max-width: 600px;
		width: 100%;
		text-align: center;
		box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
		border: 1px solid var(--el-border-color-light);
		animation: warningFadeIn 0.5s ease-out;

		.warning-icon {
			margin-bottom: 30px;

			.warning-icon-svg {
				font-size: 80px;
				color: var(--el-color-warning);
				animation: warningPulse 2s infinite;
			}
		}

		.warning-title {
			font-size: 28px;
			font-weight: 700;
			color: var(--el-color-danger);
			margin-bottom: 20px;
			line-height: 1.2;
		}

		.warning-message {
			font-size: 18px;
			color: var(--el-text-color-primary);
			margin-bottom: 30px;
			line-height: 1.5;
		}

		.warning-details {
			text-align: left;
			background: var(--el-color-warning-light-9);
			border: 1px solid var(--el-color-warning-light-7);
			border-radius: 8px;
			padding: 20px;
			margin-bottom: 30px;

			p {
				margin: 0 0 10px 0;
				font-size: 14px;
				color: var(--el-text-color-regular);
				line-height: 1.6;

				&:last-child {
					margin-bottom: 0;
				}

				&:before {
					content: '• ';
					color: var(--el-color-warning);
					font-weight: bold;
				}
			}
		}

		.warning-actions {
			display: flex;
			gap: 15px;
			justify-content: center;
			margin-bottom: 30px;

			.el-button {
				min-width: 120px;
				height: 44px;
				font-size: 16px;
				border-radius: 8px;
			}
		}

		.warning-footer {
			border-top: 1px solid var(--el-border-color-lighter);
			padding-top: 20px;

			p {
				margin: 0;
				font-size: 12px;
				color: var(--el-text-color-secondary);
				line-height: 1.5;
			}
		}
	}
}

// 动画效果
@keyframes warningFadeIn {
	from {
		opacity: 0;
		transform: scale(0.9) translateY(-20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

@keyframes warningPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}
}

// 深色模式适配
[data-theme='dark'] {
	.devtools-warning {
		background: rgba(0, 0, 0, 0.9);

		.devtools-warning-content {
			background: var(--next-bg-main);
			border-color: var(--next-border-color);
			box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);

			.warning-details {
				background: rgba(230, 162, 60, 0.1);
				border-color: rgba(230, 162, 60, 0.3);
			}
		}
	}
}

// 响应式设计
@media screen and (max-width: 768px) {
	.devtools-warning {
		.devtools-warning-content {
			padding: 40px 20px;
			margin: 20px;

			.warning-icon .warning-icon-svg {
				font-size: 60px;
			}

			.warning-title {
				font-size: 24px;
			}

			.warning-message {
				font-size: 16px;
			}

			.warning-actions {
				flex-direction: column;
				align-items: center;

				.el-button {
					width: 100%;
					max-width: 200px;
				}
			}
		}
	}
}

@media screen and (max-width: 480px) {
	.devtools-warning {
		.devtools-warning-content {
			padding: 30px 15px;

			.warning-icon .warning-icon-svg {
				font-size: 50px;
			}

			.warning-title {
				font-size: 20px;
			}

			.warning-message {
				font-size: 14px;
			}

			.warning-details {
				padding: 15px;

				p {
					font-size: 13px;
				}
			}
		}
	}
}
</style>
