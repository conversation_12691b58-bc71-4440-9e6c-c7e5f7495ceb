<template>
	<div class="devtools-warning" v-show="visible">
		<div class="devtools-warning-overlay">
			<div class="devtools-warning-content">
				<!-- 动态背景效果 -->
				<div class="warning-bg-animation"></div>

				<!-- 主要内容 -->
				<div class="warning-main-content">
					<div class="warning-icon">
						<SvgIcon name="ele-WarningFilled" class="warning-icon-svg" />
						<div class="warning-icon-glow"></div>
					</div>

					<div class="warning-title">
						<span class="title-text">{{ $t('devToolsWarning.title') }}</span>
						<div class="title-underline"></div>
					</div>

					<div class="warning-message">{{ $t('devToolsWarning.message') }}</div>

					<div class="warning-details">
						<div class="details-header">
							<SvgIcon name="ele-InfoFilled" class="details-icon" />
							<span>{{ $t('devToolsWarning.detailsTitle') }}</span>
						</div>
						<div class="details-list">
							<div class="detail-item">
								<div class="detail-dot"></div>
								<span>{{ $t('devToolsWarning.details1') }}</span>
							</div>
							<div class="detail-item">
								<div class="detail-dot"></div>
								<span>{{ $t('devToolsWarning.details2') }}</span>
							</div>
							<div class="detail-item">
								<div class="detail-dot"></div>
								<span>{{ $t('devToolsWarning.details3') }}</span>
							</div>
						</div>
					</div>

					<div class="warning-actions">
						<el-button type="primary" size="large" class="refresh-btn" @click="handleRefresh">
							<SvgIcon name="ele-RefreshRight" class="btn-icon" />
							{{ $t('devToolsWarning.refreshBtn') }}
						</el-button>
					</div>

					<div class="warning-footer">
						<div class="footer-icon">
							<SvgIcon name="ele-Shield" />
						</div>
						<p>{{ $t('devToolsWarning.footer') }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="DevToolsWarning">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

// 定义组件属性
interface Props {
	visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
});

// 定义事件
const emit = defineEmits<{
	refresh: [];
}>();

const { t } = useI18n();

// 刷新页面
const handleRefresh = () => {
	window.location.reload();
};
</script>

<style scoped lang="scss">
.devtools-warning {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
	backdrop-filter: blur(15px);
	display: flex;
	align-items: center;
	justify-content: center;

	.devtools-warning-overlay {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
		position: relative;
	}

	.devtools-warning-content {
		background: var(--el-bg-color);
		border-radius: 20px;
		max-width: 650px;
		width: 100%;
		text-align: center;
		box-shadow:
			0 25px 80px rgba(0, 0, 0, 0.4),
			0 0 0 1px rgba(255, 255, 255, 0.1);
		border: 1px solid var(--el-border-color-light);
		animation: warningFadeIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
		position: relative;
		overflow: hidden;

		.warning-bg-animation {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(45deg,
				transparent 30%,
				rgba(var(--el-color-warning-rgb), 0.03) 50%,
				transparent 70%);
			animation: bgShimmer 3s ease-in-out infinite;
		}

		.warning-main-content {
			position: relative;
			z-index: 1;
			padding: 50px 40px;
		}

		.warning-icon {
			margin-bottom: 35px;
			position: relative;
			display: inline-block;

			.warning-icon-svg {
				font-size: 90px;
				color: var(--el-color-warning);
				animation: warningPulse 2.5s ease-in-out infinite;
				filter: drop-shadow(0 0 20px rgba(var(--el-color-warning-rgb), 0.3));
			}

			.warning-icon-glow {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 120px;
				height: 120px;
				background: radial-gradient(circle, rgba(var(--el-color-warning-rgb), 0.2) 0%, transparent 70%);
				border-radius: 50%;
				animation: glowPulse 2.5s ease-in-out infinite;
			}
		}

		.warning-title {
			margin-bottom: 25px;
			position: relative;

			.title-text {
				font-size: 32px;
				font-weight: 700;
				color: var(--el-color-danger);
				line-height: 1.2;
				display: inline-block;
			}

			.title-underline {
				width: 60px;
				height: 4px;
				background: linear-gradient(90deg, var(--el-color-danger), var(--el-color-warning));
				margin: 15px auto 0;
				border-radius: 2px;
				animation: underlineExpand 0.8s ease-out 0.3s both;
			}
		}

		.warning-message {
			font-size: 20px;
			color: var(--el-text-color-primary);
			margin-bottom: 35px;
			line-height: 1.6;
			font-weight: 500;
		}

		.warning-details {
			text-align: left;
			background: linear-gradient(135deg,
				var(--el-color-warning-light-9),
				var(--el-color-warning-light-8));
			border: 1px solid var(--el-color-warning-light-6);
			border-radius: 12px;
			padding: 25px;
			margin-bottom: 35px;
			box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

			.details-header {
				display: flex;
				align-items: center;
				margin-bottom: 20px;
				font-weight: 600;
				color: var(--el-color-warning-dark-2);

				.details-icon {
					font-size: 20px;
					margin-right: 8px;
				}
			}

			.details-list {
				.detail-item {
					display: flex;
					align-items: flex-start;
					margin-bottom: 12px;
					font-size: 15px;
					color: var(--el-text-color-regular);
					line-height: 1.6;

					&:last-child {
						margin-bottom: 0;
					}

					.detail-dot {
						width: 6px;
						height: 6px;
						background: var(--el-color-warning);
						border-radius: 50%;
						margin-right: 12px;
						margin-top: 8px;
						flex-shrink: 0;
					}
				}
			}
		}

		.warning-actions {
			margin-bottom: 35px;

			.refresh-btn {
				min-width: 160px;
				height: 50px;
				font-size: 16px;
				font-weight: 600;
				border-radius: 12px;
				background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
				border: none;
				box-shadow: 0 8px 25px rgba(var(--el-color-primary-rgb), 0.3);
				transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

				.btn-icon {
					margin-right: 8px;
					font-size: 18px;
				}

				&:hover {
					transform: translateY(-2px);
					box-shadow: 0 12px 35px rgba(var(--el-color-primary-rgb), 0.4);
				}

				&:active {
					transform: translateY(0);
				}
			}
		}

		.warning-footer {
			border-top: 1px solid var(--el-border-color-lighter);
			padding-top: 25px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8px;

			.footer-icon {
				color: var(--el-color-success);
				font-size: 16px;
			}

			p {
				margin: 0;
				font-size: 14px;
				color: var(--el-text-color-secondary);
				line-height: 1.5;
			}
		}
	}
}

// 动画效果
@keyframes warningFadeIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(-30px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

@keyframes warningPulse {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.05);
		opacity: 0.9;
	}
}

@keyframes glowPulse {
	0%, 100% {
		opacity: 0.3;
		transform: translate(-50%, -50%) scale(1);
	}
	50% {
		opacity: 0.6;
		transform: translate(-50%, -50%) scale(1.1);
	}
}

@keyframes bgShimmer {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

@keyframes underlineExpand {
	from {
		width: 0;
	}
	to {
		width: 60px;
	}
}

// 深色模式适配
[data-theme='dark'] {
	.devtools-warning {
		background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(10, 10, 10, 0.98));

		.devtools-warning-content {
			background: var(--next-bg-main);
			border-color: var(--next-border-color);
			box-shadow:
				0 25px 80px rgba(0, 0, 0, 0.8),
				0 0 0 1px rgba(255, 255, 255, 0.05);

			.warning-bg-animation {
				background: linear-gradient(45deg,
					transparent 30%,
					rgba(230, 162, 60, 0.05) 50%,
					transparent 70%);
			}

			.warning-details {
				background: linear-gradient(135deg,
					rgba(230, 162, 60, 0.08),
					rgba(230, 162, 60, 0.12));
				border-color: rgba(230, 162, 60, 0.2);
			}
		}
	}
}

// 响应式设计
@media screen and (max-width: 768px) {
	.devtools-warning {
		.devtools-warning-content {
			margin: 15px;
			border-radius: 16px;

			.warning-main-content {
				padding: 40px 25px;
			}

			.warning-icon {
				margin-bottom: 25px;

				.warning-icon-svg {
					font-size: 70px;
				}

				.warning-icon-glow {
					width: 100px;
					height: 100px;
				}
			}

			.warning-title {
				margin-bottom: 20px;

				.title-text {
					font-size: 26px;
				}

				.title-underline {
					width: 50px;
					height: 3px;
				}
			}

			.warning-message {
				font-size: 18px;
				margin-bottom: 25px;
			}

			.warning-details {
				padding: 20px;
				margin-bottom: 25px;

				.details-header {
					margin-bottom: 15px;
					font-size: 15px;

					.details-icon {
						font-size: 18px;
					}
				}

				.details-list .detail-item {
					font-size: 14px;
					margin-bottom: 10px;
				}
			}

			.warning-actions {
				margin-bottom: 25px;

				.refresh-btn {
					min-width: 140px;
					height: 45px;
					font-size: 15px;
				}
			}

			.warning-footer {
				padding-top: 20px;

				p {
					font-size: 13px;
				}
			}
		}
	}
}

@media screen and (max-width: 480px) {
	.devtools-warning {
		.devtools-warning-content {
			margin: 10px;
			border-radius: 12px;

			.warning-main-content {
				padding: 30px 20px;
			}

			.warning-icon {
				margin-bottom: 20px;

				.warning-icon-svg {
					font-size: 60px;
				}

				.warning-icon-glow {
					width: 80px;
					height: 80px;
				}
			}

			.warning-title {
				.title-text {
					font-size: 22px;
				}

				.title-underline {
					width: 40px;
				}
			}

			.warning-message {
				font-size: 16px;
			}

			.warning-details {
				padding: 15px;

				.details-header {
					font-size: 14px;
					margin-bottom: 12px;

					.details-icon {
						font-size: 16px;
					}
				}

				.details-list .detail-item {
					font-size: 13px;
					margin-bottom: 8px;

					.detail-dot {
						width: 5px;
						height: 5px;
						margin-top: 6px;
					}
				}
			}

			.warning-actions .refresh-btn {
				min-width: 120px;
				height: 42px;
				font-size: 14px;

				.btn-icon {
					font-size: 16px;
				}
			}
		}
	}
}
</style>
