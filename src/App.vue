<template>
	<el-config-provider :size="getGlobalComponentSize" :locale="getGlobalI18n">
		<router-view v-show="setLockScreen && !shouldShowDevToolsWarning" />
		<LockScreen v-if="themeConfig.isLockScreen" />
		<Setings ref="settingRef" v-show="themeConfig.lockScreenTime > 1" />
		<CloseFull v-if="!themeConfig.isLockScreen" />
		<!-- 全局AI助手图标 -->
		<AIAssistantIcon v-show="themeConfig.isShowAiRobot && !shouldShowDevToolsWarning" :initial-position="aiAssistantPosition" :chat-assistant-visible="chatAssistantVisible" @click="handleAIAssistantClick" />
		<ChatAssistant ref="chatAssistantRef" />
		<!-- 开发者工具警告页面 -->
		<DevToolsWarning :visible="shouldShowDevToolsWarning" @refresh="handleRefreshPage" />
	</el-config-provider>
</template>

<script setup lang="ts" name="app">
import { useI18n } from 'vue-i18n';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import { Local, Session } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';
import setIntroduction from '/@/utils/setIconfont';
import { defineAsyncComponent, ref, computed, watch, onBeforeMount, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useDevToolsDetection } from '/@/hooks/useDevToolsDetection';
import { ElMessage } from 'element-plus';

// 引入组件
const LockScreen = defineAsyncComponent(() => import('/@/layout/lockScreen/index.vue'));
const Setings = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/setings.vue'));
const CloseFull = defineAsyncComponent(() => import('/@/layout/navBars/breadcrumb/closeFull.vue'));
const ChatAssistant = defineAsyncComponent(() => import('/@/components/ChatAssistant/index.vue'));
const AIAssistantIcon = defineAsyncComponent(() => import('/@/components/AIAssistantIcon/index.vue'));
const DevToolsWarning = defineAsyncComponent(() => import('/@/components/DevToolsWarning/index.vue'));

// 定义变量内容
const { messages, locale } = useI18n();
const settingRef = ref();
const chatAssistantRef = ref();
const route = useRoute();
const stores = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);

// 开发者工具检测
const { shouldShowWarning: shouldShowDevToolsWarning } = useDevToolsDetection();


// AI助手相关
const aiAssistantPosition = ref({ x: window.innerWidth - 100, y: window.innerHeight - 200 });
const chatAssistantVisible = ref(false);

// 处理AI助手点击事件
const handleAIAssistantClick = () => {
	// 更新ChatAssistant组件的可见性
	if (chatAssistantRef.value) {
		chatAssistantRef.value.visible = !chatAssistantRef.value.visible;
		// 同步更新chatAssistantVisible，用于AIAssistantIcon组件
		chatAssistantVisible.value = chatAssistantRef.value.visible;
	}
};

// 处理开发者工具警告页面事件
const handleRefreshPage = () => {
	window.location.reload();
};

// 设置锁屏时组件显示隐藏
const setLockScreen = computed(() => {
	// 防止锁屏后，刷新出现不相关界面
	// https://gitee.com/lyt-top/vue-next-admin/issues/I6AF8P
	return themeConfig.value.isLockScreen ? themeConfig.value.lockScreenTime > 1 : themeConfig.value.lockScreenTime >= 0;
});

// 获取全局组件大小
const getGlobalComponentSize = computed(() => {
	return other.globalComponentSize();
});
// 获取全局 i18n
const getGlobalI18n = computed(() => {
	return messages.value[locale.value];
});

let isShowAiRobot: ReturnType<typeof setInterval> | null = null;
// 检查是否显示AI助手
const startCheckIsShowAiRobot = () => {
	isShowAiRobot = setInterval(() => {
		// 判断当前是否登录
		themeConfig.value.isShowAiRobot = !!Session.getToken();
	}, 300); // 每300毫秒检查一次是否登录，使检测更频繁
};
// 设置初始化，防止刷新时恢复默认
onBeforeMount(() => {
	// 设置批量第三方 icon 图标
	setIntroduction.cssCdn();
	// 设置批量第三方 js
	setIntroduction.jsCdn();
});
// 页面加载时
onMounted(() => {
	nextTick(() => {
		// 监听布局配'置弹窗点击打开
		mittBus.on('openSetingsDrawer', () => {
			settingRef.value.openDrawer();
		});
		// 获取缓存中的布局配置
		if (Local.get('themeConfig')) {
			storesThemeConfig.setThemeConfig({ themeConfig: Local.get('themeConfig') });
			document.documentElement.style.cssText = Local.get('themeConfigStyle');
		}
		// 获取缓存中的全屏配置
		if (Session.get('isTagsViewCurrenFull')) {
			stores.setCurrenFullscreen(Session.get('isTagsViewCurrenFull'));
		}

		// 设置AI助手位置在右下角
		if (typeof window !== 'undefined') {
			aiAssistantPosition.value = {
				x: window.innerWidth - 100,
				y: window.innerHeight - 200,
			};
		}
		
		// 监听ChatAssistant组件可见性变化
		watch(() => chatAssistantRef.value?.visible, (newVal) => {
			if (typeof newVal === 'boolean') {
				chatAssistantVisible.value = newVal;
				console.log('ChatAssistant可见性状态变化:', newVal);
			}
		});

		// 检查是否显示AI助手
		startCheckIsShowAiRobot()
	});
});
// 页面销毁时，关闭监听布局配置/i18n监听
onUnmounted(() => {
	mittBus.off('openSetingsDrawer', () => {});
	if (isShowAiRobot) {
		clearInterval(isShowAiRobot);
	}
});
// 监听路由的变化，设置网站标题
watch(
	() => route.path,
	() => {
		other.useTitle();
	},
	{
		deep: true,
	}
);
</script>
