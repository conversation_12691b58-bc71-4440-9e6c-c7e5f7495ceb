/**
 * 开发者工具检测工具
 * 用于检测浏览器是否打开了开发者工具（调试模式）
 */

export interface DevToolsDetectionOptions {
	// 检测间隔时间（毫秒）
	interval?: number;
	// 是否启用检测
	enabled?: boolean;
	// 检测到开发者工具时的回调
	onDetected?: () => void;
	// 开发者工具关闭时的回调
	onClosed?: () => void;
}

export class DevToolsDetection {
	private isDetecting = false;
	private isDevToolsOpen = false;
	private timer: number | null = null;
	private options: Required<DevToolsDetectionOptions>;

	constructor(options: DevToolsDetectionOptions = {}) {
		this.options = {
			interval: 1000,
			enabled: true,
			onDetected: () => {},
			onClosed: () => {},
			...options,
		};
	}

	/**
	 * 开始检测
	 */
	start(): void {
		if (!this.options.enabled || this.isDetecting) {
			return;
		}

		this.isDetecting = true;
		this.detect();
	}

	/**
	 * 停止检测
	 */
	stop(): void {
		this.isDetecting = false;
		if (this.timer) {
			clearTimeout(this.timer);
			this.timer = null;
		}
	}

	/**
	 * 获取当前检测状态
	 */
	getDetectionStatus(): boolean {
		return this.isDevToolsOpen;
	}

	/**
	 * 更新配置
	 */
	updateOptions(options: Partial<DevToolsDetectionOptions>): void {
		this.options = { ...this.options, ...options };
	}

	/**
	 * 执行检测
	 */
	private detect(): void {
		if (!this.isDetecting) {
			return;
		}

		const isOpen = this.checkDevTools();
		
		// 状态发生变化时触发回调
		if (isOpen !== this.isDevToolsOpen) {
			this.isDevToolsOpen = isOpen;
			if (isOpen) {
				this.options.onDetected();
			} else {
				this.options.onClosed();
			}
		}

		// 继续下一次检测
		this.timer = window.setTimeout(() => {
			this.detect();
		}, this.options.interval);
	}

	/**
	 * 检测开发者工具是否打开
	 * 使用多种方法进行检测以提高准确性
	 */
	private checkDevTools(): boolean {
		// 方法1: 检测窗口大小变化（最可靠的方法）
		const threshold = 160;
		const widthThreshold = window.outerWidth - window.innerWidth > threshold;
		const heightThreshold = window.outerHeight - window.innerHeight > threshold;

		// 方法2: 检测调试器（在生产环境中使用）
		let debuggerDetected = false;
		if (import.meta.env.PROD) {
			try {
				const start = performance.now();
				// eslint-disable-next-line no-debugger
				debugger;
				const end = performance.now();
				debuggerDetected = end - start > 100;
			} catch (e) {
				// 忽略错误
			}
		}

		// 方法3: 检测控制台对象的特殊属性
		let consoleDetected = false;
		try {
			// @ts-ignore
			consoleDetected = window.console && (window.console.firebug || (window.console.exception && window.console.table));
		} catch (e) {
			// 忽略错误
		}

		// 方法4: 检测开发者工具特有的全局变量
		let devToolsDetected = false;
		try {
			// @ts-ignore
			devToolsDetected = !!(window.devtools && window.devtools.open);
		} catch (e) {
			// 忽略错误
		}

		// 综合判断 - 主要依赖窗口大小检测
		return widthThreshold || heightThreshold || debuggerDetected || consoleDetected || devToolsDetected;
	}
}

// 创建全局实例
let globalDetection: DevToolsDetection | null = null;

/**
 * 初始化全局开发者工具检测
 */
export function initDevToolsDetection(options: DevToolsDetectionOptions = {}): DevToolsDetection {
	if (globalDetection) {
		globalDetection.stop();
	}
	
	globalDetection = new DevToolsDetection(options);
	return globalDetection;
}

/**
 * 获取全局检测实例
 */
export function getDevToolsDetection(): DevToolsDetection | null {
	return globalDetection;
}

/**
 * 停止全局检测
 */
export function stopDevToolsDetection(): void {
	if (globalDetection) {
		globalDetection.stop();
		globalDetection = null;
	}
}

/**
 * 快速检测开发者工具是否打开（一次性检测）
 */
export function isDevToolsOpen(): boolean {
	const threshold = 160;
	const widthThreshold = window.outerWidth - window.innerWidth > threshold;
	const heightThreshold = window.outerHeight - window.innerHeight > threshold;
	return widthThreshold || heightThreshold;
}
