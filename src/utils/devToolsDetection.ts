/**
 * 开发者工具检测工具
 * 用于检测浏览器是否打开了开发者工具（调试模式）
 */

export interface DevToolsDetectionOptions {
	// 检测间隔时间（毫秒）
	interval?: number;
	// 是否启用检测
	enabled?: boolean;
	// 检测到开发者工具时的回调
	onDetected?: () => void;
	// 开发者工具关闭时的回调
	onClosed?: () => void;
}

export class DevToolsDetection {
	private isDetecting = false;
	private isDevToolsOpen = false;
	private timer: number | null = null;
	private options: Required<DevToolsDetectionOptions>;
	private lastWindowSize = { width: 0, height: 0 };
	private detectionHistory: boolean[] = [];

	constructor(options: DevToolsDetectionOptions = {}) {
		this.options = {
			interval: 1000,
			enabled: true,
			onDetected: () => {},
			onClosed: () => {},
			...options,
		};

		// 初始化窗口大小
		this.lastWindowSize = {
			width: window.outerWidth - window.innerWidth,
			height: window.outerHeight - window.innerHeight
		};
	}

	/**
	 * 开始检测
	 */
	start(): void {
		if (!this.options.enabled || this.isDetecting) {
			return;
		}

		this.isDetecting = true;
		this.detect();
	}

	/**
	 * 停止检测
	 */
	stop(): void {
		this.isDetecting = false;
		if (this.timer) {
			clearTimeout(this.timer);
			this.timer = null;
		}
	}

	/**
	 * 获取当前检测状态
	 */
	getDetectionStatus(): boolean {
		return this.isDevToolsOpen;
	}

	/**
	 * 更新配置
	 */
	updateOptions(options: Partial<DevToolsDetectionOptions>): void {
		this.options = { ...this.options, ...options };
	}

	/**
	 * 执行检测
	 */
	private detect(): void {
		if (!this.isDetecting) {
			return;
		}

		const isOpen = this.checkDevTools();
		
		// 状态发生变化时触发回调
		if (isOpen !== this.isDevToolsOpen) {
			this.isDevToolsOpen = isOpen;
			if (isOpen) {
				this.options.onDetected();
			} else {
				this.options.onClosed();
			}
		}

		// 继续下一次检测
		this.timer = window.setTimeout(() => {
			this.detect();
		}, this.options.interval);
	}

	/**
	 * 检测开发者工具是否打开
	 * 使用多种方法进行检测以提高准确性，防止绕过
	 */
	private checkDevTools(): boolean {
		let detectionResults = {
			windowSize: false,
			console: false,
			debugger: false,
			performance: false,
			devtools: false,
			toString: false
		};

		// 方法1: 智能窗口大小检测（包括历史记录分析）
		const sizeThreshold = 160;
		const widthDiff = window.outerWidth - window.innerWidth;
		const heightDiff = window.outerHeight - window.innerHeight;

		// 检测窗口大小变化
		const sizeChanged = Math.abs(widthDiff - this.lastWindowSize.width) > 50 ||
						   Math.abs(heightDiff - this.lastWindowSize.height) > 50;

		// 更新历史记录
		this.lastWindowSize = { width: widthDiff, height: heightDiff };

		// 统一检测策略：标准检测或检测到可疑的大小变化
		detectionResults.windowSize = (widthDiff > sizeThreshold || heightDiff > sizeThreshold) ||
									  (sizeChanged && (widthDiff > 50 || heightDiff > 50));

		// 方法2: 检测控制台对象的toString方法被重写
		try {
			let devtools = { open: false };
			let element = new Image();
			element.__defineGetter__('id', function() {
				devtools.open = true;
			});
			console.log('%c', element);
			detectionResults.toString = devtools.open;
		} catch (e) {
			// 忽略错误
		}

		// 方法3: 检测调试器
		try {
			const start = performance.now();
			// eslint-disable-next-line no-debugger
			debugger;
			const end = performance.now();
			detectionResults.debugger = end - start > 100;
		} catch (e) {
			// 忽略错误
		}

		// 方法4: 检测控制台对象的特殊属性
		try {
			// @ts-ignore
			detectionResults.console = !!(window.console &&
				(window.console.firebug ||
				 window.console.exception ||
				 (window.console.clear && window.console.clear.toString().indexOf('native code') === -1)));
		} catch (e) {
			// 忽略错误
		}

		// 方法5: 检测控制台输出监听（即使最小化也能检测到）
		try {
			let consoleOpened = false;
			const originalLog = console.log;

			// 临时重写console.log方法来检测是否有监听
			console.log = function() {
				consoleOpened = true;
				return originalLog.apply(console, arguments);
			};

			// 输出一个特殊的测试消息
			console.log('%c', 'color: transparent; font-size: 0px;');

			// 恢复原始方法
			console.log = originalLog;

			detectionResults.performance = consoleOpened;
		} catch (e) {
			// 忽略错误
		}

		// 方法6: 检测开发者工具特有的全局变量和API
		try {
			// @ts-ignore
			detectionResults.devtools = !!(window.devtools && window.devtools.open) ||
				// 检测React DevTools
				// @ts-ignore
				!!(window.__REACT_DEVTOOLS_GLOBAL_HOOK__ && window.__REACT_DEVTOOLS_GLOBAL_HOOK__.isDisabled === false) ||
				// 检测Vue DevTools
				// @ts-ignore
				!!(window.__VUE_DEVTOOLS_GLOBAL_HOOK__ && window.__VUE_DEVTOOLS_GLOBAL_HOOK__.enabled);
		} catch (e) {
			// 忽略错误
		}

		// 方法7: 检测函数toString被修改（高级检测）
		try {
			const reg = /./;
			reg.toString = function() { detectionResults.toString = true; return ''; };
			console.log(reg);
		} catch (e) {
			// 忽略错误
		}

		// 方法8: 检测时间差异（开发者工具会影响JavaScript执行时间）
		try {
			const start = Date.now();
			// 执行一些计算密集的操作
			let sum = 0;
			for (let i = 0; i < 50000; i++) {
				sum += Math.random();
			}
			const end = Date.now();
			// 如果执行时间异常长，可能是因为开发者工具的影响
			if (end - start > 200) {
				detectionResults.performance = true;
			}
		} catch (e) {
			// 忽略错误
		}

		// 综合判断：多种方法的组合
		const detectionCount = Object.values(detectionResults).filter(Boolean).length;

		// 统一检测策略：需要至少1种方法检测到
		const currentDetection = detectionCount >= 1;

		// 更新检测历史记录（保留最近10次检测结果）
		this.detectionHistory.push(currentDetection);
		if (this.detectionHistory.length > 10) {
			this.detectionHistory.shift();
		}

		// 如果最近的检测中有超过30%的检测为true，则认为开发者工具打开
		const recentPositiveDetections = this.detectionHistory.filter(Boolean).length;
		const detectionRate = recentPositiveDetections / this.detectionHistory.length;

		const isDetected = currentDetection || (this.detectionHistory.length >= 5 && detectionRate > 0.3);

		// 调试信息
		if (isDetected || detectionCount > 0) {
			console.log('DevTools Detection Results:', detectionResults);
			console.log('Detection Count:', detectionCount);
			console.log('Detection Rate:', detectionRate);
			console.log('History:', this.detectionHistory);
		}

		return isDetected;
	}
}

// 创建全局实例
let globalDetection: DevToolsDetection | null = null;

/**
 * 初始化全局开发者工具检测
 */
export function initDevToolsDetection(options: DevToolsDetectionOptions = {}): DevToolsDetection {
	if (globalDetection) {
		globalDetection.stop();
	}
	
	globalDetection = new DevToolsDetection(options);
	return globalDetection;
}

/**
 * 获取全局检测实例
 */
export function getDevToolsDetection(): DevToolsDetection | null {
	return globalDetection;
}

/**
 * 停止全局检测
 */
export function stopDevToolsDetection(): void {
	if (globalDetection) {
		globalDetection.stop();
		globalDetection = null;
	}
}

/**
 * 快速检测开发者工具是否打开（一次性检测）
 */
export function isDevToolsOpen(): boolean {
	// 统一检测策略
	const threshold = 160;
	const widthThreshold = window.outerWidth - window.innerWidth > threshold;
	const heightThreshold = window.outerHeight - window.innerHeight > threshold;
	return widthThreshold || heightThreshold;
}
